/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/target/x86_64-unknown-linux-gnu/debug/deps/libopenwrt_rust_firmware-96b446e2b2f0db66.rmeta: src/lib.rs src/memory.rs src/allocator.rs src/network/mod.rs src/network/device.rs src/network/protocols.rs src/network/interface_manager.rs src/network/wireless.rs src/network/firewall.rs src/interrupts_simple.rs src/ffi/mod.rs src/ffi/simple_test.rs src/ffi/libubox.rs src/ffi/uci.rs src/ffi/netifd.rs

/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/target/x86_64-unknown-linux-gnu/debug/deps/libopenwrt_rust_firmware-96b446e2b2f0db66.rlib: src/lib.rs src/memory.rs src/allocator.rs src/network/mod.rs src/network/device.rs src/network/protocols.rs src/network/interface_manager.rs src/network/wireless.rs src/network/firewall.rs src/interrupts_simple.rs src/ffi/mod.rs src/ffi/simple_test.rs src/ffi/libubox.rs src/ffi/uci.rs src/ffi/netifd.rs

/Users/<USER>/Documents/augment-projects/OpenWrt-Rust-BareMetal/target/x86_64-unknown-linux-gnu/debug/deps/openwrt_rust_firmware-96b446e2b2f0db66.d: src/lib.rs src/memory.rs src/allocator.rs src/network/mod.rs src/network/device.rs src/network/protocols.rs src/network/interface_manager.rs src/network/wireless.rs src/network/firewall.rs src/interrupts_simple.rs src/ffi/mod.rs src/ffi/simple_test.rs src/ffi/libubox.rs src/ffi/uci.rs src/ffi/netifd.rs

src/lib.rs:
src/memory.rs:
src/allocator.rs:
src/network/mod.rs:
src/network/device.rs:
src/network/protocols.rs:
src/network/interface_manager.rs:
src/network/wireless.rs:
src/network/firewall.rs:
src/interrupts_simple.rs:
src/ffi/mod.rs:
src/ffi/simple_test.rs:
src/ffi/libubox.rs:
src/ffi/uci.rs:
src/ffi/netifd.rs:
