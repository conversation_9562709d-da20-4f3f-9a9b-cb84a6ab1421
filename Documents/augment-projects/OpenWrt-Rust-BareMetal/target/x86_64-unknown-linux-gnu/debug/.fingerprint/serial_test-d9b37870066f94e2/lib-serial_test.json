{"rustc": 5357548097637079788, "features": "[\"async\", \"default\", \"logging\"]", "declared_features": "[\"async\", \"default\", \"docsrs\", \"file_locks\", \"logging\", \"test_logging\"]", "target": 9324353821239110715, "profile": 4612059108396444692, "path": 4600342604223753704, "deps": [[2706460456408817945, "futures", false, 10251159234912540509], [3722963349756955755, "once_cell", false, 5036017122745455362], [3963110851676444176, "scc", false, 15845205984429791817], [4495526598637097934, "parking_lot", false, 17192934714291138973], [5986029879202738730, "log", false, 130757356606123813], [8492652481582073871, "serial_test_derive", false, 8755429171942573026]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/serial_test-d9b37870066f94e2/dep-lib-serial_test", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}