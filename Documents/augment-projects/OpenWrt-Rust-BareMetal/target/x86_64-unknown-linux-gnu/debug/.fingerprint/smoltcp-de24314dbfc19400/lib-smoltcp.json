{"rustc": 5357548097637079788, "features": "[\"medium-ethernet\", \"medium-ip\", \"proto-dhcpv4\", \"proto-dns\", \"proto-igmp\", \"proto-ipv4\", \"proto-ipv6\", \"socket\", \"socket-dhcpv4\", \"socket-dns\", \"socket-icmp\", \"socket-tcp\", \"socket-udp\"]", "declared_features": "[\"_proto-fragmentation\", \"alloc\", \"assembler-max-segment-count-1\", \"assembler-max-segment-count-16\", \"assembler-max-segment-count-2\", \"assembler-max-segment-count-3\", \"assembler-max-segment-count-32\", \"assembler-max-segment-count-4\", \"assembler-max-segment-count-8\", \"async\", \"default\", \"defmt\", \"dns-max-name-size-128\", \"dns-max-name-size-255\", \"dns-max-name-size-64\", \"dns-max-result-count-1\", \"dns-max-result-count-16\", \"dns-max-result-count-2\", \"dns-max-result-count-3\", \"dns-max-result-count-32\", \"dns-max-result-count-4\", \"dns-max-result-count-8\", \"dns-max-server-count-1\", \"dns-max-server-count-16\", \"dns-max-server-count-2\", \"dns-max-server-count-3\", \"dns-max-server-count-32\", \"dns-max-server-count-4\", \"dns-max-server-count-8\", \"fragmentation-buffer-size-1024\", \"fragmentation-buffer-size-1500\", \"fragmentation-buffer-size-16384\", \"fragmentation-buffer-size-2048\", \"fragmentation-buffer-size-256\", \"fragmentation-buffer-size-32768\", \"fragmentation-buffer-size-4096\", \"fragmentation-buffer-size-512\", \"fragmentation-buffer-size-65536\", \"fragmentation-buffer-size-8192\", \"iface-max-addr-count-1\", \"iface-max-addr-count-2\", \"iface-max-addr-count-3\", \"iface-max-addr-count-4\", \"iface-max-addr-count-5\", \"iface-max-addr-count-6\", \"iface-max-addr-count-7\", \"iface-max-addr-count-8\", \"iface-max-multicast-group-count-1\", \"iface-max-multicast-group-count-1024\", \"iface-max-multicast-group-count-128\", \"iface-max-multicast-group-count-16\", \"iface-max-multicast-group-count-2\", \"iface-max-multicast-group-count-256\", \"iface-max-multicast-group-count-3\", \"iface-max-multicast-group-count-32\", \"iface-max-multicast-group-count-4\", \"iface-max-multicast-group-count-5\", \"iface-max-multicast-group-count-512\", \"iface-max-multicast-group-count-6\", \"iface-max-multicast-group-count-64\", \"iface-max-multicast-group-count-7\", \"iface-max-multicast-group-count-8\", \"iface-max-route-count-1\", \"iface-max-route-count-1024\", \"iface-max-route-count-128\", \"iface-max-route-count-16\", \"iface-max-route-count-2\", \"iface-max-route-count-256\", \"iface-max-route-count-3\", \"iface-max-route-count-32\", \"iface-max-route-count-4\", \"iface-max-route-count-5\", \"iface-max-route-count-512\", \"iface-max-route-count-6\", \"iface-max-route-count-64\", \"iface-max-route-count-7\", \"iface-max-route-count-8\", \"iface-max-sixlowpan-address-context-count-1\", \"iface-max-sixlowpan-address-context-count-1024\", \"iface-max-sixlowpan-address-context-count-128\", \"iface-max-sixlowpan-address-context-count-16\", \"iface-max-sixlowpan-address-context-count-2\", \"iface-max-sixlowpan-address-context-count-256\", \"iface-max-sixlowpan-address-context-count-3\", \"iface-max-sixlowpan-address-context-count-32\", \"iface-max-sixlowpan-address-context-count-4\", \"iface-max-sixlowpan-address-context-count-5\", \"iface-max-sixlowpan-address-context-count-512\", \"iface-max-sixlowpan-address-context-count-6\", \"iface-max-sixlowpan-address-context-count-64\", \"iface-max-sixlowpan-address-context-count-7\", \"iface-max-sixlowpan-address-context-count-8\", \"iface-neighbor-cache-count-1\", \"iface-neighbor-cache-count-1024\", \"iface-neighbor-cache-count-128\", \"iface-neighbor-cache-count-16\", \"iface-neighbor-cache-count-2\", \"iface-neighbor-cache-count-256\", \"iface-neighbor-cache-count-3\", \"iface-neighbor-cache-count-32\", \"iface-neighbor-cache-count-4\", \"iface-neighbor-cache-count-5\", \"iface-neighbor-cache-count-512\", \"iface-neighbor-cache-count-6\", \"iface-neighbor-cache-count-64\", \"iface-neighbor-cache-count-7\", \"iface-neighbor-cache-count-8\", \"ipv6-hbh-max-options-1\", \"ipv6-hbh-max-options-16\", \"ipv6-hbh-max-options-2\", \"ipv6-hbh-max-options-3\", \"ipv6-hbh-max-options-32\", \"ipv6-hbh-max-options-4\", \"ipv6-hbh-max-options-8\", \"libc\", \"log\", \"medium-ethernet\", \"medium-ieee802154\", \"medium-ip\", \"packetmeta-id\", \"phy-raw_socket\", \"phy-tuntap_interface\", \"proto-dhcpv4\", \"proto-dns\", \"proto-igmp\", \"proto-ipsec\", \"proto-ipsec-ah\", \"proto-ipsec-esp\", \"proto-ipv4\", \"proto-ipv4-fragmentation\", \"proto-ipv6\", \"proto-ipv6-fragmentation\", \"proto-ipv6-hbh\", \"proto-ipv6-routing\", \"proto-rpl\", \"proto-sixlowpan\", \"proto-sixlowpan-fragmentation\", \"reassembly-buffer-count-1\", \"reassembly-buffer-count-16\", \"reassembly-buffer-count-2\", \"reassembly-buffer-count-3\", \"reassembly-buffer-count-32\", \"reassembly-buffer-count-4\", \"reassembly-buffer-count-8\", \"reassembly-buffer-size-1024\", \"reassembly-buffer-size-1500\", \"reassembly-buffer-size-16384\", \"reassembly-buffer-size-2048\", \"reassembly-buffer-size-256\", \"reassembly-buffer-size-32768\", \"reassembly-buffer-size-4096\", \"reassembly-buffer-size-512\", \"reassembly-buffer-size-65536\", \"reassembly-buffer-size-8192\", \"rpl-parents-buffer-count-16\", \"rpl-parents-buffer-count-2\", \"rpl-parents-buffer-count-32\", \"rpl-parents-buffer-count-4\", \"rpl-parents-buffer-count-8\", \"rpl-relations-buffer-count-1\", \"rpl-relations-buffer-count-128\", \"rpl-relations-buffer-count-16\", \"rpl-relations-buffer-count-2\", \"rpl-relations-buffer-count-32\", \"rpl-relations-buffer-count-4\", \"rpl-relations-buffer-count-64\", \"rpl-relations-buffer-count-8\", \"socket\", \"socket-dhcpv4\", \"socket-dns\", \"socket-icmp\", \"socket-mdns\", \"socket-raw\", \"socket-tcp\", \"socket-udp\", \"std\", \"verbose\"]", "target": 17611012386780966603, "profile": 1056490057198206251, "path": 5735425532386932447, "deps": [[2828590642173593838, "cfg_if", false, 10788201602000086572], [3712811570531045576, "byteorder", false, 8456051398768521580], [10435729446543529114, "bitflags", false, 10835294738057649859], [12740221742494834345, "heapless", false, 2852946646237728985], [14857113974570377512, "managed", false, 10334491757790693366], [15635206729440195786, "build_script_build", false, 13481667097089962797]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/smoltcp-de24314dbfc19400/dep-lib-smoltcp", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}