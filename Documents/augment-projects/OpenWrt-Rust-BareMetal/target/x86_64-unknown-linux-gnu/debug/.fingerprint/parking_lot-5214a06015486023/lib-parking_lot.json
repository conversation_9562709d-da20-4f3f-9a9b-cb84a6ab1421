{"rustc": 5357548097637079788, "features": "[]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"hardware-lock-elision\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\"]", "target": 9887373948397848517, "profile": 4612059108396444692, "path": 7635392820602258278, "deps": [[4269498962362888130, "parking_lot_core", false, 10912753372474600264], [8081351675046095464, "lock_api", false, 15369049238039185850]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/parking_lot-5214a06015486023/dep-lib-parking_lot", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}