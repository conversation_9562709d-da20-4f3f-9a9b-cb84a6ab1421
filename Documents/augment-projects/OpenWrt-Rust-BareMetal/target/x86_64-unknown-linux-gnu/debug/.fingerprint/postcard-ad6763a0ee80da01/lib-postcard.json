{"rustc": 5357548097637079788, "features": "[\"heapless\"]", "declared_features": "[\"alloc\", \"crc\", \"default\", \"defmt\", \"embedded-io\", \"embedded-io-04\", \"embedded-io-06\", \"experimental-derive\", \"heapless\", \"heapless-cas\", \"paste\", \"postcard-derive\", \"use-crc\", \"use-defmt\", \"use-std\"]", "target": 7941872121969890562, "profile": 1056490057198206251, "path": 331734390624776492, "deps": [[5232917721369523709, "heapless", false, 7665550114317208980], [9689903380558560274, "serde", false, 14964349660700517736], [13902618262990712667, "cobs", false, 15441691504359984976]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/postcard-ad6763a0ee80da01/dep-lib-postcard", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}