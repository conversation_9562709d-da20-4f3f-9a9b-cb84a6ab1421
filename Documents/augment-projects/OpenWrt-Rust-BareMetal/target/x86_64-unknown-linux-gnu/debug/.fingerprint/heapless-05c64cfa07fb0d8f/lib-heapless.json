{"rustc": 5357548097637079788, "features": "[\"serde\"]", "declared_features": "[\"__trybuild\", \"atomic-polyfill\", \"cas\", \"default\", \"defmt\", \"defmt-impl\", \"mpmc_large\", \"serde\", \"ufmt-impl\", \"ufmt-write\", \"x86-sync-pool\"]", "target": 488718209059184808, "profile": 4612059108396444692, "path": 10867745457508531718, "deps": [[768992478617479281, "hash32", false, 704256364035652556], [2313368913568865230, "spin", false, 10033542137644575782], [4462517779602467004, "stable_deref_trait", false, 3390045764120276536], [5232917721369523709, "build_script_build", false, 1295610180938647846], [9689903380558560274, "serde", false, 18328670264074654693]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/heapless-05c64cfa07fb0d8f/dep-lib-heapless", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}