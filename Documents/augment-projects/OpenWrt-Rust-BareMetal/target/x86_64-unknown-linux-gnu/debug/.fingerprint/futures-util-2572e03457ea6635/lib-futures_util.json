{"rustc": 5357548097637079788, "features": "[\"alloc\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 16475848742078660876, "path": 6011282182865037972, "deps": [[5103565458935487, "futures_io", false, 11790522647777444796], [1615478164327904835, "pin_utils", false, 18068296400536795388], [1811549171721445101, "futures_channel", false, 12508741842170575690], [1906322745568073236, "pin_project_lite", false, 3953151217186333477], [5451793922601807560, "slab", false, 9912432093764554231], [7013762810557009322, "futures_sink", false, 14263989627608074432], [7620660491849607393, "futures_core", false, 1508238986855192186], [15932120279885307830, "memchr", false, 9719869678272542624], [16240732885093539806, "futures_task", false, 7356600657791437747]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-util-2572e03457ea6635/dep-lib-futures_util", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}