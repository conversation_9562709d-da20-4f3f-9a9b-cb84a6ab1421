{"rustc": 5357548097637079788, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 4612059108396444692, "path": 18420539683731305239, "deps": [[555019317135488525, "regex_automata", false, 1094259012583974539], [9408802513701742484, "regex_syntax", false, 18105105300335474686]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/regex-9daa1574918a8dc4/dep-lib-regex", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}