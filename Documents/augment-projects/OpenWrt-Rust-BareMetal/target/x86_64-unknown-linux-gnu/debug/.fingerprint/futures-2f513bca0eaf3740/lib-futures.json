{"rustc": 5357548097637079788, "features": "[\"alloc\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 16475848742078660876, "path": 12816429852546212729, "deps": [[5103565458935487, "futures_io", false, 11790522647777444796], [1811549171721445101, "futures_channel", false, 12508741842170575690], [7013762810557009322, "futures_sink", false, 14263989627608074432], [7620660491849607393, "futures_core", false, 1508238986855192186], [10629569228670356391, "futures_util", false, 3828941183756420825], [12779779637805422465, "futures_executor", false, 17703717940376717841], [16240732885093539806, "futures_task", false, 7356600657791437747]], "local": [{"CheckDepInfo": {"dep_info": "x86_64-unknown-linux-gnu/debug/.fingerprint/futures-2f513bca0eaf3740/dep-lib-futures", "checksum": false}}], "rustflags": ["-C", "link-arg=-nostartfiles", "-C", "link-arg=-static", "-C", "relocation-model=static"], "config": 2069994364910194474, "compile_kind": 13270707523875659407}