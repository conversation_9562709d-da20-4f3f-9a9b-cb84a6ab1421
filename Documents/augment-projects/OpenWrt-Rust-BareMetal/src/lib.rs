//! OpenWrt Rust Bare Metal Firmware Library
//! 
//! This library provides the core functionality for a Rust-based
//! bare metal firmware implementation for OpenWrt systems.
//! 
//! ## Architecture Overview
//! 
//! The firmware is organized into several key modules:
//! - `memory`: Memory management and allocation
//! - `network`: Network stack and packet processing
//! - `interrupts`: Interrupt handling and system events
//! - `ffi`: Safe FFI wrappers for C library integration
//! - `drivers`: Hardware abstraction layer
//! 
//! ## Performance Features
//! 
//! - Zero-copy network processing
//! - Memory pool optimization
//! - Interrupt coalescing
//! - Cache-aligned data structures

#![no_std]
#![no_main]
#![feature(panic_info_message)]
#![feature(alloc_error_handler)]
#![feature(asm_const)]
#![feature(const_mut_refs)]

extern crate alloc;

// Core modules
pub mod memory;
pub mod allocator;
pub mod network;
pub mod interrupts_simple;
pub use interrupts_simple as interrupts;
pub mod ffi;

// Architecture-specific modules
#[cfg(target_arch = "x86_64")]
pub mod arch {
    pub mod x86_64;
    pub use x86_64::*;
}

#[cfg(target_arch = "aarch64")]
pub mod arch {
    pub mod aarch64;
    pub use aarch64::*;
}

#[cfg(target_arch = "riscv64")]
pub mod arch {
    pub mod riscv64;
    pub use riscv64::*;
}

// Re-exports for convenience
pub use memory::{PhysicalAddress, VirtualAddress};
pub use allocator::{init_heap, get_allocation_stats};
pub use network::{init_network, process_packets};
pub use interrupts::{init_interrupts, get_stats as get_interrupt_stats};

/// Firmware initialization sequence
pub fn init_firmware() -> Result<(), &'static str> {
    // Initialize memory management
    memory::init()?;
    
    // Initialize heap allocator
    allocator::init_heap()?;
    
    // Initialize interrupt handling
    interrupts::init_interrupts()?;
    
    // Initialize network stack
    network::init_network()?;
    
    // Initialize architecture-specific features
    arch::init()?;
    
    Ok(())
}

/// Main firmware loop
pub fn firmware_main_loop() -> ! {
    loop {
        // Process network packets
        network::process_packets();
        
        // Handle any pending interrupts
        interrupts::process_pending();
        
        // Yield CPU to prevent busy waiting
        arch::cpu_relax();
    }
}

/// Firmware shutdown sequence
pub fn shutdown_firmware() {
    // Graceful shutdown sequence
    network::shutdown();
    interrupts::disable_all();
    memory::cleanup();
}

/// Performance monitoring interface
pub mod perf {
    use super::*;
    
    #[derive(Debug, Clone)]
    pub struct SystemMetrics {
        pub memory_usage: memory::MemoryUsage,
        pub network_stats: network::NetworkMetrics,
        pub interrupt_stats: interrupts::InterruptStats,
    }
    
    pub fn get_system_metrics() -> SystemMetrics {
        SystemMetrics {
            memory_usage: memory::get_usage_stats(),
            network_stats: network::get_metrics(),
            interrupt_stats: interrupts::get_stats(),
        }
    }
    
    pub fn print_performance_summary() {
        let metrics = get_system_metrics();
        
        // This would typically log to a debug interface
        // For embedded systems, this might be UART or network logging
        #[cfg(feature = "debug-output")]
        {
            println!("=== System Performance Summary ===");
            println!("Memory Usage: {:?}", metrics.memory_usage);
            println!("Network Stats: {:?}", metrics.network_stats);
            println!("Interrupt Stats: {:?}", metrics.interrupt_stats);
        }
    }
}

/// Configuration management
pub mod config {
    /// Firmware configuration parameters
    #[derive(Debug, Clone)]
    pub struct FirmwareConfig {
        pub heap_size: usize,
        pub network_buffer_size: usize,
        pub max_interrupts_per_second: u32,
        pub enable_zero_copy: bool,
        pub enable_interrupt_coalescing: bool,
    }
    
    impl Default for FirmwareConfig {
        fn default() -> Self {
            Self {
                heap_size: 64 * 1024, // 64KB
                network_buffer_size: 2048,
                max_interrupts_per_second: 10000,
                enable_zero_copy: true,
                enable_interrupt_coalescing: true,
            }
        }
    }
    
    static mut FIRMWARE_CONFIG: FirmwareConfig = FirmwareConfig {
        heap_size: 64 * 1024,
        network_buffer_size: 2048,
        max_interrupts_per_second: 10000,
        enable_zero_copy: true,
        enable_interrupt_coalescing: true,
    };
    
    pub fn get_config() -> &'static FirmwareConfig {
        unsafe { &FIRMWARE_CONFIG }
    }
    
    pub fn update_config(new_config: FirmwareConfig) {
        unsafe {
            FIRMWARE_CONFIG = new_config;
        }
    }
}

/// Error handling utilities
pub mod error {
    #[derive(Debug, Clone, Copy, PartialEq, Eq)]
    pub enum FirmwareError {
        MemoryError,
        NetworkError,
        InterruptError,
        ConfigurationError,
        HardwareError,
    }
    
    impl core::fmt::Display for FirmwareError {
        fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
            match self {
                FirmwareError::MemoryError => write!(f, "Memory management error"),
                FirmwareError::NetworkError => write!(f, "Network stack error"),
                FirmwareError::InterruptError => write!(f, "Interrupt handling error"),
                FirmwareError::ConfigurationError => write!(f, "Configuration error"),
                FirmwareError::HardwareError => write!(f, "Hardware abstraction error"),
            }
        }
    }
    
    pub type Result<T> = core::result::Result<T, FirmwareError>;
}

/// Utility functions and macros
pub mod utils {
    /// Align value to the next boundary
    pub const fn align_up(value: usize, alignment: usize) -> usize {
        (value + alignment - 1) & !(alignment - 1)
    }
    
    /// Align value to the previous boundary
    pub const fn align_down(value: usize, alignment: usize) -> usize {
        value & !(alignment - 1)
    }
    
    /// Check if value is aligned
    pub const fn is_aligned(value: usize, alignment: usize) -> bool {
        value & (alignment - 1) == 0
    }
    
    /// Convert bytes to human-readable format
    pub fn format_bytes(bytes: usize) -> alloc::string::String {
        use alloc::format;
        
        const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;
        
        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }
        
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

// Architecture-specific implementations
#[cfg(target_arch = "x86_64")]
mod arch_impl {
    pub fn init() -> Result<(), &'static str> {
        // x86_64-specific initialization
        Ok(())
    }
    
    pub fn cpu_relax() {
        unsafe {
            core::arch::asm!("pause");
        }
    }
}

#[cfg(target_arch = "aarch64")]
mod arch_impl {
    pub fn init() -> Result<(), &'static str> {
        // AArch64-specific initialization
        Ok(())
    }
    
    pub fn cpu_relax() {
        unsafe {
            core::arch::asm!("yield");
        }
    }
}

#[cfg(target_arch = "riscv64")]
mod arch_impl {
    pub fn init() -> Result<(), &'static str> {
        // RISC-V specific initialization
        Ok(())
    }
    
    pub fn cpu_relax() {
        // RISC-V doesn't have a standard yield instruction
        // Use a simple nop or hint instruction
        unsafe {
            core::arch::asm!("nop");
        }
    }
}

// Make arch_impl available as arch
pub use arch_impl as arch;
